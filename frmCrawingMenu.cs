using DevExpress.LookAndFeel;
using DevExpress.XtraSplashScreen;
using HIH.Framework.AutoCrawingManager.Process;
using HIH.Framework.AutoCrawingManager.Result;
using HIHWCFServiceAPP;
using Microsoft.Web.WebView2.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.Framework.AutoCrawingManager
{
    public partial class frmCrawingMenu : HIH.Framework.BaseUIDX.BaseBarEditForm
    {
        public frmCrawingMenu()
        {
            InitializeComponent();
            this.webView.CoreWebView2InitializationCompleted += WebView_CoreWebView2InitializationCompleted;
            this.webView.NavigationCompleted += WebView_NavigationCompleted;
            this.webView.NavigationStarting += WebView_NavigationStarting;
           

            this.bar1.LinksPersistInfo.Clear();
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
                new DevExpress.XtraBars.LinkPersistInfo(this.StartExecuteBtn),
                new DevExpress.XtraBars.LinkPersistInfo(this.EndExecuteBtn),
                new DevExpress.XtraBars.LinkPersistInfo(this.CompareBtn),
                new DevExpress.XtraBars.LinkPersistInfo(this.btnExit,true)
            });
        }

        private BindingList<ISearchItem> SearchKeyList = new BindingList<ISearchItem>();

        private List<IProcess> ShippingList = new List<IProcess>();
        private readonly ReaderWriterLockSlim LockSlim = new ReaderWriterLockSlim();
        private Dictionary<ulong, bool> SignCount = new Dictionary<ulong, bool>();
        private volatile bool isReadOperationInProgress = false;
        private volatile bool readOperationCompleted = false;
        private volatile bool isRetryInProgress = false;
        private TaskCompletionSource<bool> currentReadCompletionSource = null;

        private readonly string OrderTableName = "[uswms].[dbo].[订单表]";
        private DataTable OrderDT = new DataTable();

        private readonly string CustomerCodeTableName = "[uswms].[dbo].[用户项目表]";
        private DataTable CustomerCodeDT = new DataTable();

        private readonly string ContainerTableName = "[uswms].[dbo].[船公司集装箱明细爬取]";
        private DataTable SearchContainerDT = new DataTable();



        IOverlaySplashScreenHandle waitHandle = null;

        IOverlaySplashScreenHandle ShowProgressPanel()
        {
            return SplashScreenManager.ShowOverlayForm(this);
        }
        void CloseProgressPanel(IOverlaySplashScreenHandle handle)
        {
            if (handle != null)
                SplashScreenManager.CloseOverlayForm(handle);
        }


        #region web事件
        private void WebView_NavigationStarting(object sender, CoreWebView2NavigationStartingEventArgs e)
        {
            MineDel(e.NavigationId);

        }
        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            //Console.WriteLine(e.NavigationId);
            //Console.WriteLine("跳转完成");
            //Console.WriteLine(DateTime.Now);
            //判断网页是否正确
            //如果网页正确
            string currentUrl = this.webView.CoreWebView2.Source.ToString();
            //Console.WriteLine(currentUrl);

            MineAdd(e.NavigationId);

        }

        //信号量 s 表示初始可用资源为 1,总可用资源为1

        private void CoreWebView2_NewWindowRequested(object sender, CoreWebView2NewWindowRequestedEventArgs e)
        {
            e.NewWindow = (CoreWebView2)sender;
        }

        private void WebView_CoreWebView2InitializationCompleted(object sender, CoreWebView2InitializationCompletedEventArgs e)
        {
            this.webView.CoreWebView2.NewWindowRequested += CoreWebView2_NewWindowRequested;
            this.webView.CoreWebView2.DOMContentLoaded += CoreWebView2_DOMContentLoaded;
        }
        private void CoreWebView2_DOMContentLoaded(object sender, CoreWebView2DOMContentLoadedEventArgs e)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("var button = document.createElement('button');\r\n");
            sb.Append("button.innerHTML = '模拟输入';\r\n");
            sb.Append("button.setAttribute(\"id\", \"customerI\");\r\n");
            sb.Append("button.style.display = \"none\";\r\n");
            sb.Append("document.body.appendChild(button);\r\n");
            sb.Append("button.addEventListener('click', function() {\r\n");
            sb.Append("var trElements = document.getElementsByTagName('tr');\r\n");
            sb.Append("console.log(trElements)\r\n");
            sb.Append("if (trElements.length > 2)\r\n");
            sb.Append("{\r\n");
            sb.Append("var trElement = trElements[2];\r\n");
            sb.Append("console.log(trElement)\r\n");
            sb.Append("var tdElements = trElement.children;\r\n");
            sb.Append("if (tdElements.length > 5)\r\n");
            sb.Append("{\r\n");
            sb.Append("var tdElement = tdElements[5];\r\n");
            sb.Append("console.log(tdElement)\r\n");
            sb.Append("var aElements = tdElement.children;\r\n");
            sb.Append("for (let i = 0; i < aElements.length; i++)\r\n");
            sb.Append("{\r\n");
            sb.Append("if (aElements[i].innerText == 'B/L Data')\r\n");
            sb.Append("{\r\n");
            sb.Append("console.log(aElements[i])\r\n");
            sb.Append("var aList = aElements[i].children;\r\n");
            sb.Append("console.log(aList)\r\n");
            sb.Append("if (aList.length > 0)\r\n");
            sb.Append("{\r\n");
            sb.Append("for(let j=0;j<aList.length;j++)\r\n");
            sb.Append("{\r\n");
            sb.Append("if(aList[j].nodeName == 'A')\r\n");
            sb.Append("{\r\n");
            sb.Append("aList[j].click();\r\n");
            sb.Append("return\r\n");
            sb.Append("}\r\n");
            sb.Append("}\r\n");
            sb.Append("}\r\n");
            sb.Append("return;\r\n");
            sb.Append("}\r\n");
            sb.Append("}\r\n");
            sb.Append("}\r\n");
            sb.Append("}\r\n");
            sb.Append(" });\r\n");
            Task<string> ts = this.webView.CoreWebView2.ExecuteScriptAsync(sb.ToString());
        }

        #endregion

        #region 锁
        private void MineDel(ulong navigationId)
        {
            LockSlim.EnterWriteLock();
            try
            {
                if (SignCount.ContainsKey(navigationId))
                {
                    SignCount[navigationId] = false;
                }
                else
                {
                    SignCount.Add(navigationId, false);
                }

            }
            catch (Exception exc)
            {
                throw;
            }
            finally
            {
                LockSlim.ExitWriteLock();
            }
        }

        private void MineAdd(ulong navigationId)
        {
            LockSlim.EnterWriteLock();
            try
            {
                if (SignCount.ContainsKey(navigationId))
                {
                    SignCount[navigationId] = true;
                }
                else
                {
                    throw new Exception("未有该页面在开始跳转");
                }

            }
            catch (Exception exc)
            {
                throw;
            }
            finally
            {
                LockSlim.ExitWriteLock();
            }
        }

        private bool MineRead()
        {
            LockSlim.EnterReadLock();
            try
            {
                foreach (var item in SignCount)
                {
                    if (!item.Value)
                    {
                        return false;
                    }
                }
                return true;

            }
            catch (Exception exc)
            {
                throw;
            }
            finally
            {
                LockSlim.ExitReadLock();
            }
        }

        private void MineClear()
        {
            LockSlim.EnterWriteLock();
            try
            {
                SignCount.Clear();
            }
            catch (Exception exc)
            {
                throw;
            }
            finally
            {
                LockSlim.ExitWriteLock();
            }
        }

        /// <summary>
        /// 根据操作类型获取最优延迟时间
        /// </summary>
        /// <param name="processType">操作类型</param>
        /// <returns>延迟时间（毫秒）</returns>
        private int GetOptimalStepDelay(IProcessType processType)
        {
            switch (processType)
            {
                case IProcessType.JUMPTO:
                    return 500; // 页面跳转需要较短延迟，因为有导航完成事件
                case IProcessType.OPERATE:
                    return 300; // DOM操作需要短暂延迟
                case IProcessType.READ:
                    return 100; // 读取操作延迟最短
                default:
                    return 500;
            }
        }
        #endregion

        private async void frmMain_Load(object sender, EventArgs e)
        {
            try
            {
                waitHandle = this.ShowProgressPanel();
                await webView.EnsureCoreWebView2Async();

                this.ShippingList.Add(new ICMA());
                this.ShippingList.Add(new ISSPH());
                this.ShippingList.Add(new ICOSCO());
                this.ShippingList.Add(new IEMC());
                this.ShippingList.Add(new IHAPAG());
                this.ShippingList.Add(new IHMM());
                this.ShippingList.Add(new IMSC());
                this.ShippingList.Add(new IMSK());
                this.ShippingList.Add(new IONE());
                this.ShippingList.Add(new IOOCL());
                this.ShippingList.Add(new IWHL());
                this.ShippingList.Add(new IZIM());
                this.SetCustomerNumCombo();
                this.Init();
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
            finally
            {
                this.CloseProgressPanel(waitHandle);
            }
        }
        private void SetCustomerNumCombo()
        {
            try
            {
                this.CustomerCodeDT.Clear();
                this.CustomerCodeDT = GetDataList(this.CustomerCodeTableName, new List<SearchInfo>() {
                    new SearchInfo("用户名",ICF.ISO.UNAME,SqlOperator.Equal),
                    new SearchInfo("默认","desc",SqlOperator.Order)
                });

                this.CustomerNumCombo.Properties.Items.Clear();

                foreach (DataRow dr in this.CustomerCodeDT.Rows)
                {
                    this.CustomerNumCombo.Properties.Items.Add(dr["客户代号"].ToString());
                }

                if (this.CustomerNumCombo.Properties.Items.Count > 0)
                {
                    var firstItem = this.CustomerNumCombo.Properties.Items[0];
                    firstItem.CheckState = CheckState.Checked; // 设置第一个项为选中状态
                }


            }
            catch (Exception exc)
            {
                throw;
            }
        }

        public void Init()
        {
            try
            {
                this.gd.DataSource = null;
                this.SearchKeyList.Clear();
                this.OrderDT.Clear();

                SQL.Clear();
                SQL.Add(new SearchInfo("订单类型", this.OrderTypeEdit.Text, SqlOperator.Equal));
                SQL.Add(new SearchInfo("提单号", "", SqlOperator.IsNotNull));

                if (!string.IsNullOrEmpty(cbbCGS.Text))
                {
                    SQL.Add(new SearchInfo("船公司", this.cbbCGS.Text, SqlOperator.Equal));
                }

                if (this.IsNoneForOption.Checked)
                {
                    SQL.Add(new SearchInfo("操作完成时间", "", SqlOperator.IsNull, false, "cz"));
                    SQL.Add(new SearchInfo("操作完成时间", "", SqlOperator.Equal, false, "cz"));
                }
                else
                {
                    SQL.Add(new SearchInfo("操作完成时间", "", SqlOperator.IsNotNull, false, "cz"));
                    SQL.Add(new SearchInfo("操作完成时间", "", SqlOperator.NotEqual, false, "cz"));
                }

                if (!string.IsNullOrEmpty(this.OrderNumEdit.Text))
                {
                    SQL.Add(new SearchInfo("订单号", this.OrderNumEdit.Text, SqlOperator.Like));
                }
                if (!string.IsNullOrEmpty(this.BlNoEdit.Text))
                {
                    SQL.Add(new SearchInfo("提单号", this.BlNoEdit.Text, SqlOperator.Like));
                }

                List<object> customerNumList = this.CustomerNumCombo.Properties.GetItems().GetCheckedValues();
                if (customerNumList.Count > 0)
                {
                    foreach (object customerNum in customerNumList)
                    {
                        SQL.Add(new SearchInfo("客户代号", customerNum.ToString(), SqlOperator.Equal, false, "khdh"));
                    }
                }
                else
                {
                    SQL.Add(new SearchInfo("客户代号", "", SqlOperator.Equal, false, "khdh"));
                }

                if (this.OrderStartTime.Checked)
                    SQL.Add(new SearchInfo("订单时间", this.OrderStartTime.Value.ToString("yyyy-MM-dd") + " 00:00:00", SqlOperator.MoreThanOrEqual));

                if (this.OrderEndTime.Checked)
                    SQL.Add(new SearchInfo("订单时间", this.OrderEndTime.Value.ToString("yyyy-MM-dd") + " 23:59:59", SqlOperator.LessThanOrEqual));

                if (this.ETAStartTime.Checked)
                    SQL.Add(new SearchInfo("ETA", this.ETAStartTime.Value.ToString("yyyy-MM-dd") + " 00:00:00", SqlOperator.MoreThanOrEqual));

                if (this.ETAEndTime.Checked)
                    SQL.Add(new SearchInfo("ETA", this.ETAEndTime.Value.ToString("yyyy-MM-dd") + " 23:59:59", SqlOperator.LessThanOrEqual));


                SQL.Add(new SearchInfo("订单时间", "Desc", SqlOperator.Order));
                SQL.Add(new SearchInfo("船公司", "Desc", SqlOperator.Order));

                this.OrderDT = GetDataList(this.OrderTableName, SQL);

                foreach (DataRow dr in this.OrderDT.Rows)
                {
                    string eta = "无";
                    if (DateTime.TryParse(dr["ETA"].ToString(), out DateTime EtaDat))
                    {
                        eta = EtaDat.ToString("yyyy-MM-dd");
                    }

                    this.SearchKeyList.Add(new ISearchItem()
                    {
                        OrderNumber = dr["订单号"].ToString(),
                        SearchKey = dr["提单号"].ToString(), //这里是因为有的搜索条件是要集装箱，后续看看要不要根据船公司去变
                        ShippingCompany = dr["船公司"].ToString(),
                        PriETA = eta,
                        BLNo = dr["提单号"].ToString(),
                        Status = QUESTATUS.WAIT,
                        LastTime = dr["船舶状态更新时间"].ToString(),
                        Result = "等待执行中..."
                    });
                }
                this.ShowLabelCountLabel.Text = "一共" + this.SearchKeyList.Count + "条";
                this.gd.DataSource = this.SearchKeyList;

            }
            catch (Exception exc)
            {
                throw;
            }
        }


        private void gdv_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
        {

            if (e.Column.FieldName == "ShowDetail")
            {
                if (e.CellValue != null
                    && e.CellValue.ToString() != ""
                    && e.CellValue.ToString().Contains(">"))
                {
                    e.Appearance.ForeColor = DXSkinColors.ForeColors.Critical;
                }
            }
        }

        private void SearchBtn_Click(object sender, EventArgs e)
        {
            try
            {
                waitHandle = this.ShowProgressPanel();
                if (this.SearchKeyList.Any(it => it.Status != QUESTATUS.WAIT))
                {
                    if (ICF.ISD.ShowYesNoAndTips("当前也有爬取的内容，搜索将会清空，是否继续？"))
                    {
                        this.Init();
                    }
                }
                else
                {
                    this.Init();
                }

            }
            catch(Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
            finally
            {
                this.CloseProgressPanel(waitHandle);
            }
        }
        private void StartExecuteBtn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (this.SearchKeyList.Any(it => it.Status != QUESTATUS.WAIT))
                {
                    if (ICF.ISD.ShowYesNoAndTips("是否清空重新爬取？是:清空,否:只爬取失败的"))
                    {
                        this.Init();
                    }
                }

                this.StartExecuteBtn.Enabled = false;
                this.CompareBtn.Enabled = false;
                this.btnExit.Enabled = false;
                this.EndExecuteBtn.Enabled = true;

                this.CloseSearch();

                this.bworker.RunWorkerAsync();

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
        private void EndExecuteBtn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.bworker.CancelAsync();
        }

        private void CompareBtn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                object vOutData;
                bool ios = ICF.ISD.SubFormShowModalWithReturn("HIH.Framework.AutoCrawingManager.dll", "HIH.Framework.AutoCrawingManager.frmOrderResultForm", "比对查询", SearchKeyList, out vOutData);
                if (ios)
                {
                    this.gd.DataSource = null;
                    if (vOutData == null)
                        throw new Exception("未获取到处理后的结果");
                    BindingList<ISearchItem> list = (BindingList<ISearchItem>)vOutData;
                    this.SearchKeyList.Clear();
                    foreach (ISearchItem item in list)
                    {
                        this.SearchKeyList.Add(item);
                    }
                    this.ShowLabelCountLabel.Text = "一共" + this.SearchKeyList.Count + "条";
                    this.gd.DataSource = this.SearchKeyList;
                    this.gd.RefreshDataSource();

                }
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }
        private void CloseSearch()
        {
            try
            {
                this.OrderNumEdit.ReadOnly = true;
                this.BlNoEdit.ReadOnly = true;
                this.CustomerNumCombo.ReadOnly = true;
                this.OrderStartTime.Enabled = false;
                this.OrderEndTime.Enabled = false;
                this.IsNoneForOption.Enabled = false;
                this.TimerSpit.ReadOnly = true;

                this.ExSearchBtn.Enabled = false;

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }


        private void OpenSearch()
        {
            try
            {
                this.OrderNumEdit.ReadOnly = false;
                this.BlNoEdit.ReadOnly = false;
                this.CustomerNumCombo.ReadOnly = false;
                this.OrderStartTime.Enabled = true;
                this.OrderEndTime.Enabled = true;
                this.IsNoneForOption.Enabled = true;
                this.TimerSpit.ReadOnly = false;

                this.ExSearchBtn.Enabled = true;

            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void bworker_DoWork(object sender, DoWorkEventArgs e)
        {
            foreach (ISearchItem searchItem in SearchKeyList)
            {
                int overAllIndex = 1;
                foreach (IProcess process in this.ShippingList)
                {
                    if (process.ProcessName == searchItem.ShippingCompany)
                    {
                        List<IProcessItem> ProcessList = new List<IProcessItem>();
                        if (process.ProcessName == "HAPAG")
                        {
                            ProcessList = process.Run(searchItem.OrderNumber, process.ReplaceText);
                        }
                        else
                        {
                            ProcessList = process.Run(searchItem.SearchKey, process.ReplaceText);
                        }
                        ProcessList.Sort((x, y) => x.SerialNumber.CompareTo(y.SerialNumber));
                        List<string> processNameList = ProcessList.Select(p => p.ProcessItemName).ToList();
                        foreach (IProcessItem processItem in ProcessList)
                        {
                            this.NextStep(overAllIndex, new IProcessData {
                                ShippingCompany = process.ProcessName,
                                Item = processItem,
                                NameList = processNameList,
                                SearchItem = searchItem
                            });
                            if (this.bworker.CancellationPending)
                                return;

                            // 优化：根据操作类型调整延迟时间
                            int stepDelay = GetOptimalStepDelay(processItem.PType);
                            if (stepDelay > 0)
                                Thread.Sleep(stepDelay);
                        }
                        overAllIndex++;
                        break;
                    }

                }
            }
        }

        private void NextStep(int index, IProcessData processData)
        {
            try
            {
                while (true)
                {
                    //也就是说，给了地址，他不是立刻就去跳转的，
                    //在给了地址之后到执行跳转会有时间差，而在这个时间断里，各个状态早就可以走完了，
                    //所以要先等待1秒
                    int waitStepTime = (int)this.TimerSpit.Value;
                    Thread.Sleep(waitStepTime * 1000);
                    if (MineRead())
                    {
                        // 同步执行操作，不使用异步的ReportProgress
                        ExecuteProcessItemSync(processData);
                        return;
                    }
                }

            }
            catch (Exception exc)
            {
                throw;
            }
        }

        /// <summary>
        /// 同步执行ProcessItem操作
        /// </summary>
        /// <param name="processData">处理数据</param>
        private void ExecuteProcessItemSync(IProcessData processData)
        {
            ISearchItem searchItem = processData.SearchItem;
            IProcessItem processItem = processData.Item;

            // 更新UI显示（必须在UI线程中执行）
            this.Invoke(new Action(() => {
                this.ChildStepBar.Items.Clear();
                foreach (string pName in processData.NameList)
                {
                    this.ChildStepBar.Items.Add(pName);
                }
                this.ChildStepBar.SelectedItemIndex = processItem.SerialNumber;
            }));

            // 根据操作类型执行相应操作
            switch (processItem.PType)
            {
                case IProcessType.JUMPTO:
                    this.Invoke(new Action(() => {
                        this.webView.CoreWebView2.Navigate(processItem.JScript);
                    }));
                    break;
                case IProcessType.OPERATE:
                    this.Invoke(new Action(() => {
                        this.webView.CoreWebView2.ExecuteScriptAsync(processItem.JScript);
                    }));
                    break;
                case IProcessType.READ:
                    // READ操作需要同步等待完成
                    ExecuteReadSync(processItem, searchItem);
                    break;
            }
        }

        /// <summary>
        /// 同步执行READ操作
        /// </summary>
        /// <param name="processItem">处理项</param>
        /// <param name="searchItem">搜索项</param>
        private void ExecuteReadSync(IProcessItem processItem, ISearchItem searchItem)
        {
            isReadOperationInProgress = true;
            readOperationCompleted = false;

            Console.WriteLine($"[DEBUG] 开始同步READ操作: {processItem.ProcessItemName}");

            // 在UI线程中启动READ操作
            this.Invoke(new Action(() => {
                ExecuteReadWithRetryAsync(processItem, searchItem);
            }));

            // 在后台线程中等待完成
            WaitForReadOperation();

            Console.WriteLine($"[DEBUG] 同步READ操作完成: {processItem.ProcessItemName}");
        }

        /// <summary>
        /// 等待READ操作完成
        /// </summary>
        private void WaitForReadOperation()
        {
            // 等待READ操作完成，考虑重试时间：3次重试 * 10秒间隔 + 执行时间
            int maxWaitTime = 60000; // 最大等待60秒
            int waitInterval = 200; // 每200ms检查一次
            int totalWaited = 0;

            // 等待TaskCompletionSource完成，同时检查重试状态
            while ((currentReadCompletionSource != null && !currentReadCompletionSource.Task.IsCompleted) ||
                   isRetryInProgress ||
                   isReadOperationInProgress)
            {
                if (totalWaited >= maxWaitTime)
                    break;

                Thread.Sleep(waitInterval);
                totalWaited += waitInterval;

                // 检查是否被取消
                if (this.bworker.CancellationPending)
                    break;

                // 输出等待状态
                if (totalWaited % 2000 == 0) // 每2秒输出一次状态
                {
                    Console.WriteLine($"[DEBUG] 等待中... 已等待{totalWaited}ms, 重试中:{isRetryInProgress}, 操作中:{isReadOperationInProgress}");
                }
            }

            // 如果超时，强制完成
            if (totalWaited >= maxWaitTime && currentReadCompletionSource != null && !currentReadCompletionSource.Task.IsCompleted)
            {
                this.Invoke(new Action(() => {
                    CompleteReadOperation(false); // 超时视为失败
                }));
            }
        }

        private void bworker_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            IProcessData processData = (IProcessData)e.UserState;
            ISearchItem searchItem = processData.SearchItem;
            IProcessItem processItem = processData.Item;
            //this.ShippingCompanyLabel.Text = processData.ShippingCompany;
            this.ChildStepBar.Items.Clear();
            foreach (string pName in processData.NameList)
            {
                this.ChildStepBar.Items.Add(pName);
            }
            this.ChildStepBar.SelectedItemIndex = processItem.SerialNumber;

            switch (processItem.PType)
            {
                case IProcessType.JUMPTO:
                    this.webView.CoreWebView2.Navigate(processItem.JScript);
                    break;
                case IProcessType.OPERATE:
                    this.webView.CoreWebView2.ExecuteScriptAsync(processItem.JScript);
                    break;
                case IProcessType.READ:
                    isReadOperationInProgress = true;
                    readOperationCompleted = false;

                    // 添加调试信息
                    Console.WriteLine($"[DEBUG] 开始READ操作: {processItem.ProcessItemName}");

                    ExecuteReadWithRetryAsync(processItem, searchItem);
                    break;

            }

        }

        /// <summary>
        /// 带重试机制的读取执行方法
        /// </summary>
        /// <param name="processItem">处理项</param>
        /// <param name="searchItem">搜索项</param>
        /// <returns>返回Task以支持异步等待</returns>
        private Task ExecuteReadWithRetryAsync(IProcessItem processItem, ISearchItem searchItem)
        {
            int maxRetries = 3; // 最大重试次数
            int retryDelayMs = 10000; // 重试间隔（毫秒）

            var completionSource = new TaskCompletionSource<bool>();

            // 将completionSource存储到实例变量中，避免重复设置
            currentReadCompletionSource = completionSource;

            ExecuteReadAttempt(processItem, searchItem, 1, maxRetries, retryDelayMs);

            return completionSource.Task;
        }

        /// <summary>
        /// 执行单次读取尝试
        /// </summary>
        /// <param name="processItem">处理项</param>
        /// <param name="searchItem">搜索项</param>
        /// <param name="currentAttempt">当前尝试次数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔</param>
        private void ExecuteReadAttempt(IProcessItem processItem, ISearchItem searchItem, int currentAttempt, int maxRetries, int retryDelayMs)
        {
            Task<string> task = this.webView.CoreWebView2.ExecuteScriptAsync(processItem.JScript);
            task.ContinueWith(t =>
            {
                try
                {
                    string resultString = t.Result.Replace("\\u003C", "<").Replace("\\n", "").Replace("\\", "");
                    IResult result = processItem.Interpreter(resultString);

                    // 检查结果是否需要重试
                    if (ShouldRetryResult(result, resultString) && currentAttempt < maxRetries)
                    {
                        Console.WriteLine($"[DEBUG] 需要重试，当前尝试: {currentAttempt}/{maxRetries}, Status: {result.Status}");

                        // 设置重试状态
                        isRetryInProgress = true;

                        // 更新状态显示重试信息
                        searchItem.Status = QUESTATUS.WAIT;

                        // 刷新界面显示
                        this.Invoke(new Action(() => {
                            this.gd.RefreshDataSource();
                        }));

                        // 等待后重试 - 使用同步等待确保重试期间不会继续下一个processItem
                        //Console.WriteLine($"[DEBUG] 开始等待{retryDelayMs}ms后重试");

                        // 在后台线程中同步等待，然后重试
                        Task.Run(() => {
                            Thread.Sleep(retryDelayMs);
                            this.Invoke(new Action(() => {
                                //Console.WriteLine($"[DEBUG] 开始第{currentAttempt + 1}次重试");
                                isRetryInProgress = false; // 重试开始时清除重试状态
                                ExecuteReadAttempt(processItem, searchItem, currentAttempt + 1, maxRetries, retryDelayMs);
                            }));
                        });
                        return; // 重试时直接返回，不执行finally块
                    }

                    // 处理最终结果 - 只有不需要重试时才到这里
                    searchItem.ContainerList = result.ContainerList;

                    if (!string.IsNullOrEmpty(result.ETAExc))
                    {
                        if (currentAttempt >= maxRetries)
                        {
                            throw new Exception($"重试{maxRetries}次后仍然失败：{result.ETAExc}");
                        }
                        else
                        {
                            throw new Exception(result.ETAExc);
                        }
                    }

                    searchItem.SeaETA = result.ETA;
                    searchItem.ShowDetail = searchItem.PriETA != searchItem.SeaETA
                                            ? searchItem.PriETA + ">" + searchItem.SeaETA
                                            : searchItem.PriETA;

                    searchItem.Status = QUESTATUS.SUCCESS;
                    searchItem.Result = "已完成爬取比对";

                    // 成功完成，设置TaskCompletionSource
                    CompleteReadOperation(true);
                }
                catch (Exception exc)
                {
                    searchItem.Status = QUESTATUS.FAIL;
                    searchItem.Result = exc.Message;

                    // 发生异常，设置TaskCompletionSource
                    CompleteReadOperation(false);
                }
                finally
                {
                    string lastTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    this.Invoke(new Action(() => {
                        this.UpShipCompanyContainer(searchItem.OrderNumber, searchItem, lastTime);
                        this.gd.RefreshDataSource();
                    }));
                }
            });
        }

        /// <summary>
        /// 完成READ操作，设置TaskCompletionSource
        /// </summary>
        /// <param name="success">是否成功</param>
        private void CompleteReadOperation(bool success)
        {
            Console.WriteLine($"[DEBUG] CompleteReadOperation 被调用，success: {success}");

            if (currentReadCompletionSource != null && !currentReadCompletionSource.Task.IsCompleted)
            {
                Console.WriteLine($"[DEBUG] 设置 TaskCompletionSource 结果: {success}");
                currentReadCompletionSource.SetResult(success);
                currentReadCompletionSource = null; // 清空引用，防止重复使用
            }
            else
            {
                Console.WriteLine($"[DEBUG] TaskCompletionSource 为空或已完成，跳过设置");
            }

            // 重置READ操作状态
            isReadOperationInProgress = false;
            isRetryInProgress = false;
            readOperationCompleted = true;
            Console.WriteLine($"[DEBUG] READ操作状态已重置");
        }

        /// <summary>
        /// 判断结果是否需要重试
        /// </summary>
        /// <param name="result">解析结果</param>
        /// <param name="resultString">原始结果字符串</param>
        /// <returns>是否需要重试</returns>
        private bool ShouldRetryResult(IResult result, string resultString)
        {
            // 如果Status返回-1，表示需要重试
            if (result.Status == -1)
                return true;

            // 其他情况不重试
            return false;
        }

        private void UpShipCompanyContainer(string orderId, ISearchItem item, string lastTime)
        {
            try
            {
                if (string.IsNullOrEmpty(orderId))
                    throw new Exception("订单编号不可为空");

                if (item == null || item.ContainerList == null)
                    return;
                Dictionary<string, string> upDic = new Dictionary<string, string>();
                foreach (IResultContainer containerItem in item.ContainerList)
                {

                    if (!string.IsNullOrEmpty(containerItem.ContainerNo))
                    {
                        this.SearchContainerDT.Clear();
                        this.SearchContainerDT = GetDataList(this.ContainerTableName, new List<SearchInfo>() {
                            new SearchInfo("订单号",orderId,SqlOperator.Equal),
                            new SearchInfo("集装箱号",containerItem.ContainerNo,SqlOperator.Equal),
                        });

                        if (this.SearchContainerDT.Rows.Count > 0)
                        {
                            string Id = this.SearchContainerDT.Rows[0]["ID"].ToString();
                            upDic.Clear();
                            if (!string.IsNullOrEmpty(containerItem.ContainerPickupTime))
                            {
                                upDic.Add("提箱日期", containerItem.ContainerPickupTime);
                            }
                            if (!string.IsNullOrEmpty(containerItem.UnloadingTime))
                            {
                                upDic.Add("卸船日期", containerItem.UnloadingTime);
                            }
                            if (!string.IsNullOrEmpty(containerItem.ReturnTime))
                            {
                                upDic.Add("还箱日期", containerItem.ReturnTime);
                            }

                            if (upDic.Count > 0)
                            {
                                Update(this.ContainerTableName, "ID", Id, upDic);
                            }

                        }else{
                            upDic.Clear();
                            upDic.Add("更新时间", lastTime);
                            upDic.Add("订单号", orderId);
                            upDic.Add("提单号", item.BLNo);
                            upDic.Add("集装箱号", containerItem.ContainerNo);

                            if (!string.IsNullOrEmpty(containerItem.ContainerPickupTime))
                            {
                                upDic.Add("提箱日期", containerItem.ContainerPickupTime);
                            }
                            if (!string.IsNullOrEmpty(containerItem.UnloadingTime))
                            {
                                upDic.Add("卸船日期", containerItem.UnloadingTime);
                            }
                            if (!string.IsNullOrEmpty(containerItem.ReturnTime))
                            {
                                upDic.Add("还箱日期", containerItem.ReturnTime);
                            }
                            Insert(this.ContainerTableName, upDic);
                        }

                    }

                }
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }
        }

        private void bworker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            this.StartExecuteBtn.Enabled = true;
            this.CompareBtn.Enabled = true;
            this.btnExit.Enabled = true;
            this.EndExecuteBtn.Enabled = false;

            this.OpenSearch();

            this.MineClear();

            ICF.ISD.ShowTips("执行完成");
        }


    }
}
